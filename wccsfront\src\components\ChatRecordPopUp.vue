<template>
  <el-dialog
    v-model="dialogVisible"
    :title="title"
    width="60%"
    :before-close="handleClose"
    class="chat-record-dialog"
  >
    <div class="chat-record-content">
      <div v-for="(item, index) in chatRecords" :key="index" class="chat-message">
        <div class="message-header">
          <div class="sender-info">
            <span class="sender-name">{{ item.sender }}</span>
            <span class="sender-label" :class="getLabelClass(item.sender_label)">
              @{{ item.sender_label }}
            </span>
          </div>
          <span class="message-time">{{ item.timestamp }}</span>
        </div>
        <div class="message-body" :class="{ 'is-received': item.type === 'received' }">
          <!-- 文本消息 -->
          <div v-if="item.msgtype === 'text'" class="text-message">
            {{ item.content }}
          </div>
          <!-- 图片消息 -->
          <div v-else-if="item.msgtype === 'image'" class="image-message">
            <el-image 
              :src="item.content" 
              :preview-src-list="[item.content]"
              fit="cover"
            />
          </div>
          <!-- 文件消息 -->
          <div v-else-if="item.msgtype === 'file'" class="file-message">
            <el-button type="primary" link>
              <el-icon><Document /></el-icon>
              {{ item.filename }}
            </el-button>
            <span class="file-size">{{ formatFileSize(item.filesize) }}</span>
          </div>
        </div>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch, onMounted } from 'vue'
import { Document } from '@element-plus/icons-vue'

const props = defineProps({
  title: {
    type: String,
    default: '聊天记录'//弹窗标题，默认是聊天记录
  },
  chatRecords: {
    type: Array,
    default: () => []
  },
  visible: {
    type: Boolean,
    default: false//弹窗是否可见，默认是关闭的
  }
})

// 定义emit，用于更新visible属性和关闭弹窗
const emit = defineEmits(['update:visible', 'close'])

// 定义dialogVisible，用于控制弹窗的可见性
const dialogVisible = ref(props.visible)

// 监听父组件传入的 visible 属性变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
})

// 监听本地 dialogVisible 变化，同步回父组件
watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

const handleClose = () => {
  dialogVisible.value = false
  emit('close')
}

const getLabelClass = (label) => {
  switch (label?.toLowerCase()) {
    case '员工':
      return 'label-employee'
    case '微信':
      return 'label-wechat'
    default:
      return 'label-default'
  }
}

const formatFileSize = (size) => {
  if (!size) return '0 B'
  const units = ['B', 'KB', 'MB', 'GB']
  let index = 0
  let fileSize = size

  while (fileSize >= 1024 && index < units.length - 1) {
    fileSize /= 1024
    index++
  }

  return `${fileSize.toFixed(2)} ${units[index]}`
}

const currentChatRecord = ref(null)

onMounted(() => {
    currentChatRecord.value = {
        title: props.title,
        item: props.chatRecords
    }
})

</script>

<style scoped>
.chat-record-dialog {
  --el-dialog-padding-primary: 20px;
}

.chat-record-content {
  max-height: 60vh;
  overflow-y: auto;
  padding: 0 10px;
}

.chat-message {
  margin-bottom: 20px;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.sender-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.sender-name {
  font-weight: 500;
  color: #333;
}

.sender-label {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.label-employee {
  background-color: #e6f4ff;
  color: #1677ff;
}

.label-wechat {
  background-color: #f6ffed;
  color: #52c41a;
}

.label-default {
  background-color: #f5f5f5;
  color: #666;
}

.message-time {
  font-size: 12px;
  color: #999;
}

.message-body {
  margin-left: 20px;
  max-width: 80%;
}

.message-body.is-received {
  margin-left: 0;
  margin-right: 20px;
}

.text-message {
  background-color: #f5f5f5;
  padding: 12px 16px;
  border-radius: 8px;
  line-height: 1.5;
  word-break: break-word;
}

.image-message {
  display: inline-block;
  max-width: 300px;
}

.image-message :deep(.el-image) {
  border-radius: 8px;
  overflow: hidden;
}

.file-message {
  background-color: #f5f5f5;
  padding: 12px 16px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.file-size {
  color: #999;
  font-size: 12px;
}

.dialog-footer {
  text-align: right;
  margin-top: 20px;
}
</style>