<!-- TODO：
固定搜索框的宽度 -->
<template>
    <div class="mainContainer-employeechatlist">
        <div class="selected-employee-card">
            <div class="profile-box">
                <div class="avatar">
                    <img src="../../../assets/人员头像.png" alt="人员头像">
                </div>
            </div>
            <div class="employee-info">
                <p class="name">{{ selectedEmployeeName }}</p>
            </div>
        </div>

        <div class="search" style="width: 600px;">
            <el-input placeholder="搜索" v-model="searchWord" clearable class="search-input" style="width: 200px;">
                <template #prefix>
                    <el-icon class="search-icon">
                        <Search />
                    </el-icon>
                </template>
            </el-input>

            <el-switch v-model="hasCommunicated" inline-prompt active-text="已沟通" inactive-text="全部" />
        </div>

        <!-- @click="getDetailChat("employee","employee")" -->
        <div class="lable-navigator">
            <el-tabs v-model="activeName" class="employeechatList-tabs" @tab-click="handleClick">
                <el-tab-pane label="员工" name="first">
                    <div class="chat-staff-card" v-for="staff in staffList" :key="staff.name"
                        @click="handleChatClick(staff, 'employee')">
                        <div class="profile">
                            <img class="staff-avatar" src="@/assets/人员头像.png" alt="人员头像">
                        </div>
                        <div class="info">
                            <p class="name">{{ staff.name }}</p>
                            <p class="latest-message"></p>
                        </div>

                    </div>
                </el-tab-pane>

                <el-tab-pane label="客户" name="second">
                    <div class="chat-staff-card" v-for="customer in staffList" :key="customer.name"
                        @click="handleChatClick(customer, 'customer')">
                        <div class="profile">
                            <img class="staff-avatar" :src="customer.avatar" alt="用户头像">
                        </div>
                        <div class="info">
                            <p class="name">{{ customer.name }}</p>
                            <p class="latest-message"></p>
                        </div>
                    </div>
                </el-tab-pane>

                <el-tab-pane label="群聊" name="third">
                    <div class="chat-staff-card" v-for="(group, index) in staffList" :key="index"
                        @click="handleChatClick(group, 'groupchat')">
                        <div class="profile group-avatar">
                            <img src="../../../assets/群聊头像.png" alt="群聊头像">
                        </div>
                        <div class="info">
                            <p class="name">{{ getGroupName(group) }}</p>
                            <p class="latest-message"></p>
                        </div>
                    </div>
                </el-tab-pane>
            </el-tabs>
        </div>

        <div class="pageContainer">
            <el-pagination v-model:current-page="currentPage" background :size="size" layout="total, prev, pager, next"
                :total="totalStaff" :page-size="pageSize" :pager-count="3" small @current-change="handlePageChange">
            </el-pagination>
        </div>
    </div>
</template>

<script setup>
import { Search } from '@element-plus/icons-vue';
import { ref, watch, inject } from 'vue';
import axiosInstance from '@/axiosConfig.js';
import { ElMessage } from 'element-plus';

const searchWord = ref(''); // searchWord 响应搜索框
const activeName = ref('first') // activeName 响应 tab 切换，以及分页查询时的会话类型传参

const staffList = ref([]); // 会话列表
const totalStaff = ref(''); // totalEmployees 分页组件展示的人员总数
const currentPage = ref(1); // currentPage 当前页码
const pageSize = ref(20); // pageSize 每页显示数量

const hasCommunicated = ref(false); // 控制是否只显示已沟通的会话



// 注入selectedEmployee，如果没有提供则默认为null
const selectedEmployee = inject('selectedEmployee', null);
const selectedEmployeeName = ref('未选择员工'); // selectedEmployeeName 选中员工姓名
// const selectedEmployeeAvatar = ref(require('@/assets/人员头像.png'))

//注入选中的会话
const selectedChat = inject('selectedChat', null);

// 监听选中员工的变化，当有员工被选中时执行相应操作
watch(selectedEmployee, (newValue) => {
    if (newValue) {
        console.log('EmployeeChatList：监听到选中员工');
        selectedEmployeeName.value = newValue.name;
        // selectedEmployeeAvatar.value = newValue.avatar;

        //重置当前页数
        currentPage.value = 1;
        //重置当前激活标签
        activeName.value = 'first'
        //重置搜索词
        searchWord.value = ''

        //获取选中人员的会话列表
        getChatList(selectedEmployee.value, currentPage.value, pageSize.value, 'employee', searchWord.value);
    }
});



// 获取存档人员列表
const getChatList = (selectedEmployee, currentPage, pageSize, staffChatType, searchWord) => {
    const jwt_token = localStorage.getItem('access_token')
    axiosInstance.post('/api/chatmessage/conversations', {
        from: 'employee',
        to: staffChatType,
        id: selectedEmployee.id,
        page: currentPage,
        limit: pageSize,
        name: searchWord,
        hasConversation: hasCommunicated.value
    }, {
        headers: { Authorization: 'Bearer ' + jwt_token }
    }).then(res => {
        console.log(res.data.length);
        staffList.value = res.data.data.data;
        //数量
        totalStaff.value = res.data.data.total;
    }).catch(error => {
        console.log(error);
        ElMessage.error('获取选中人员会话列表失败，请检查网络或联系管理员');
    });
}

//TODO：时间格式化
// const formatTime = (time) => {
//     const now = new Date();
//     const messageDate = new Date(time);
//     const messageYear = messageDate.getFullYear();
//     const currentYear = now.getFullYear();
//     if (messageYear === currentYear) {
//         if (messageDate.getMonth() === now.getMonth() && messageDate.getDate() === now.getDate()) {
//             // return messageDate.getHours() + ':' + messageDate.getMinutes();
//             return `${String(messageDate.getHours().padStart(2, '0'))}:${String(messageDate.getMinutes().padStart(2, '0'))}`;
//         } else {
//             return `${String(messageDate.getMonth()).padStart(2, '0')}-${String(messageDate.getDate()).padStart(2, '0')}`;
//         }
//     } else {
//         return `${String(messageDate.getFullYear())}-${String(messageDate.getMonth()).padStart(2, '0')}-${String(messageDate.getDate()).padStart(2, '0')}`;
//     }
// }


//页码变化时调用
const handlePageChange = (page) => {
    currentPage.value = page;//当前页码
    //通过activeName来判断会话类型传参
    //创建一个map集合
    const chatTypeMap = new Map([
        ['first', 'employee'],
        ['second', 'customer'],
        ['third', 'groupchat']
    ]);
    const chatType = chatTypeMap.get(activeName.value);

    //重置搜索词
    searchWord.value = ''

    getChatList(selectedEmployee.value, currentPage.value, pageSize.value, chatType, searchWord.value);
}


// 搜索框变化时调用
const filterChatList = () => {
    if (!selectedEmployee.value) {
        ElMessage.warning('请先在左侧面板选择要查看的人员');
        return;
    }

    //通过activeName来判断会话类型传参
    //创建一个map集合
    const chatTypeMap = new Map([
        ['first', 'employee'],
        ['second', 'customer'],
        ['third', 'groupchat']
    ]);
    const chatType = chatTypeMap.get(activeName.value);

    if (searchWord.value) {
        currentPage.value = 1; // 重置为第一页
    }
    getChatList(selectedEmployee.value, currentPage.value, pageSize.value, chatType, searchWord.value);
}


watch(searchWord, filterChatList);

watch(hasCommunicated, filterChatList);


//会话类型标签被点击时
const handleClick = () => {
    //通过activeName来判断会话类型传参
    //创建一个map集合

    const chatTypeMap = new Map([
        ['first', 'employee'],
        ['second', 'customer'],
        ['third', 'groupchat']
    ]);
    const chatType = chatTypeMap.get(activeName.value);

    if (!selectedEmployee.value) {
        ElMessage.warning('请先在左侧面板选择要查看的人员');
        return;
    }
    getChatList(selectedEmployee.value, currentPage.value, pageSize.value, chatType, searchWord.value);
}


//某一会话被点击时
const handleChatClick = (staff, chattype) => {
    let from = '' //会话发起方id，如果 type = groupchat，为群聊ID；
    let to = '' //会话接收方id，如果 type = groupchat，为 null；默认为员工id

    let fromName = '' //会话发起方名称，如果 type = groupchat，为群聊名称；
    let fromLable = '' //会话发起方标签，如果 type = groupchat，为null；
    let fromAvatar = ''

    let toName = '' //会话接收方名称，如果 type = groupchat，为 null；
    let toAvatar = '' //会话接收方头像 ，如果 type = groupchat，为 null；
    let toLable = '' //会话接收方类型 ，如果 type = groupchat，为 null,为employee时是员工的标签，默认是‘员工’；

    let type = '' //会话接收方类型，群聊 type = groupchat，
    let page = 1
    let limit = 30

    if (chattype == 'employee') {
        from = selectedEmployee.value.id
        to = staff.userid

        fromName = selectedEmployee.value.name
        fromLable = '@员工'
        fromAvatar = require('../../../assets/人员头像.png')

        toName = staff.name
        toAvatar = ''
        toLable = '@员工'

        type = ''
    }
    else if (chattype == 'customer') {
        from = selectedEmployee.value.id
        to = staff.externalUserId

        fromName = selectedEmployee.value.name
        fromLable = '@员工'
        fromAvatar = require('../../../assets/人员头像.png')

        toName = staff.name
        toAvatar = staff.avatar
        toLable = getCustomerLable(staff)
        type = ''
    }
    else if (chattype == 'groupchat') {
        from = staff.chatId
        to = ''

        fromName = getGroupName(staff)
        fromLable = ''
        fromAvatar = ''
        toName = ''
        toAvatar = ''
        toLable = ''

        type = 'groupchat'
    }

    const selectedChat_info = {
        from: from,
        to: to,

        fromName: fromName,
        fromLable: fromLable,
        fromAvatar: fromAvatar,

        toName: toName,
        toAvatar: toAvatar,
        toLable: toLable,

        type: type,//groupchat（群聊），其它一律视为非群聊
        page: page,
        limit: limit
    }
    selectedChat.value = selectedChat_info

    console.log('检测到某一会话被点击', selectedChat_info)

}

const getCustomerLable = (customer) => {
    switch (customer.type) {
        case 1:
            return "@微信";
        case 2:
            return customer.corpName ? `@${customer.corpName}` : '@未知企业';
        default:
            return "";
    }

}

// --------------------------------------------------------------------------控制群名显示
const getGroupName = (group) => {
    if (group.name) {
        return group.name
    }
    const groupId = group.chatId
    if (groupId && groupId.length > 6) {
        return groupId.slice(0, 11) + '...'
    }
    return ''
}

</script>

<style>
.mainContainer-employeechatlist {
    margin: 0;
    height: calc(100vh - 6.969rem);
    background-color: #ffffff;
    display: flex;
    flex-direction: column;
    width: 17.5rem;
    padding: 1rem 0.75rem;
    border-right: 0.0625rem solid #e6e6e6;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.02);
}

.mainContainer-employeechatlist .search {
    display: flex;
    flex-direction: row;

}

.mainContainer-employeechatlist .el-input {
    display: flex;
    flex-direction: row;
    margin-right: 1.1rem;
}


.selected-employee-card {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 0.75rem;
    margin-bottom: 1rem;
    background-color: #f5f7fa;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

.selected-employee-card .profile-box {
    margin-right: 0.75rem;
}

.selected-employee-card .avatar {
    /* background: linear-gradient(135deg, #1890ff, #0960bd); */
    background-color: #ffffff;
    width: 2.5rem;
    height: 2.5rem;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 0.5rem;
    font-weight: 500;
    /* box-shadow: 0 0.125rem 0.25rem rgba(24, 144, 255, 0.1); */
}

.selected-employee-card .employee-info {
    flex: 1;
}

.selected-employee-card .employee-info .name {
    font-size: 0.9375rem;
    font-weight: 500;
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.selected-employee-card .employee-info .status {
    font-size: 0.75rem;
    color: #67c23a;
}





.lable-navigator {
    flex: 1;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
}

.lable-navigator::-webkit-scrollbar {
    width: 0.375rem;
}

.lable-navigator::-webkit-scrollbar-thumb {
    background-color: #e0e0e0;
    border-radius: 0.1875rem;
}

.lable-navigator::-webkit-scrollbar-track {
    background-color: transparent;
}

.el-tabs--card {
    height: 100%;
    /* overflow-y: auto; */
}

.el-tab-pane {
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
}

.chat-staff-card {
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 4rem;
    padding: 0 0.75rem;
    margin: 0.25rem 0;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.chat-staff-card:hover {
    background-color: #f5f7fa;
    transform: translateX(0.125rem);
}

.chat-staff-card .profile {
    /* background: linear-gradient(135deg, #1890ff, #0960bd); */
    /* color: #ffffff; */
    width: 2.5rem;
    height: 2.5rem;
    display: flex;
    justify-content: center;
    align-items: center;
    /* border-radius: 0.5rem; */
    margin-right: 0.75rem;
    /* font-weight: 500; */

}

.chat-staff-card .profile img {
    width: 100%;
    height: 100%;
    object-fit: cover;

}


.chat-staff-card .info {
    flex: 1;
    overflow: hidden;
}

.chat-staff-card .info .name {
    font-size: 0.875rem;
    font-weight: 500;
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.chat-staff-card .info .latest-message {
    font-size: 0.75rem;
    color: #909399;

    width: fit-content;
    /*使宽度适应内容 */
    width: 15ch;
    /*最大宽度为15个字符 */
    white-space: nowrap;
    /*禁止换行 */
    overflow: hidden;
    /*隐藏超出内容 */
    text-overflow: ellipsis;
    /*超出部分用省略号表示 */
}

.chat-staff-card .latest-message-time {
    font-size: 0.75rem;
    color: #909399;
    width: fit-content;
    /*使宽度适应内容 */
    /* width: 15ch; */
}

.pageContainer {
    display: flex;
    justify-content: center;
    padding: 0.75rem 0;
    margin-top: 0.5rem;
    border-top: 0.0625rem solid #f0f0f0;
    width: 100%;
}

.pageContainer :deep(.el-pagination) {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    padding: 0 0.5rem;
    font-size: 0.8125rem;
}

.pageContainer :deep(.el-pagination .el-pagination__total) {
    min-width: auto;
    margin-right: 0.5rem;
    font-size: 0.8125rem;
    color: #606266;
}

.pageContainer :deep(.el-pagination .el-pager) {
    margin: 0;
}

.pageContainer :deep(.el-pagination .el-pager li) {
    min-width: 1.5rem;
    height: 1.5rem;
    line-height: 1.5rem;
    font-weight: normal;
    margin: 0 0.125rem;
}

.pageContainer :deep(.el-pagination .el-pager li.active) {
    background-color: #1890ff;
    color: #ffffff;
}

.pageContainer :deep(.el-pagination button) {
    min-width: 1.5rem;
    height: 1.5rem;
    margin: 0;
    padding: 0;
}

.pageContainer :deep(.el-pagination .btn-prev),
.pageContainer :deep(.el-pagination .btn-next) {
    margin: 0 0.125rem;
    background-color: #f4f4f5;
}

.pageContainer :deep(.el-pagination .el-icon) {
    font-size: 0.75rem;
}

.pageContainer :deep(.el-pagination .more) {
    background-color: transparent;
}

.employeechatList-tabs {
    height: 100%;
}

.employeechatList-tabs :deep(.el-tabs__header) {
    margin-bottom: 1rem;
}

.employeechatList-tabs :deep(.el-tabs__nav-wrap::after) {
    height: 0.0625rem;
    background-color: #f0f0f0;
}

.employeechatList-tabs :deep(.el-tabs__item) {
    font-size: 0.875rem;
    color: #606266;
    padding: 0 1rem;
}

.employeechatList-tabs :deep(.el-tabs__item.is-active) {
    color: #1890ff;
    font-weight: 500;
}

.employeechatList-tabs :deep(.el-tabs__active-bar) {
    background-color: #1890ff;
    height: 0.125rem;
}

.el-switch {
    --el-switch-off-color: #848487;
}

.el-switch__core{
    padding: 3px;
}
</style>
