{"version": 3, "file": "js/258.cb15757c.js", "mappings": "g8OAoCA,MAAMA,GAAaC,EAAAA,EAAAA,KAAI,GAGjBC,GAAkBC,EAAAA,EAAAA,IAAO,mBAEzBC,GAAwBD,EAAAA,EAAAA,IAAO,yBAC/BE,GAA4BF,EAAAA,EAAAA,IAAO,6BAEnCG,GAAkBH,EAAAA,EAAAA,IAAO,kBAAkB,OAG3CI,GAAmBJ,EAAAA,EAAAA,IAAO,oBAC1BK,GAAmBL,EAAAA,EAAAA,IAAO,oBAC1BM,GAAeN,EAAAA,EAAAA,IAAO,gBAEtBO,EAAgBC,IAOlB,GAJAJ,EAAiBK,MAAQ,KACzBJ,EAAiBI,MAAQ,KACzBH,EAAaG,MAAQ,KAET,mBAATD,EACC,OAEJE,QAAQC,IAAI,iBAAiBH,GAC7BL,EAAgBM,MAAQD,EAExB,MAAMI,EAAQ,CACV,MAAO,CAAC,OAAQ,QAChB,MAAO,CAAC,OAAQ,QAChB,MAAO,CAAC,OAAQ,SAEpBb,EAAgBU,MAAQG,EAAMJ,IAAU,CAAC,OAAQ,QAEjD,MAAMK,EAAc,CAChB,MAAO,eACP,MAAO,eACP,MAAO,aAEXZ,EAAsBQ,MAAQI,EAAYL,IAAU,eAEpD,MAAMM,EAAkB,CACpB,MAAO,mBACP,MAAO,mBACP,MAAO,iBAEXZ,EAA0BO,MAAQK,EAAgBN,IAAU,kBAAiB,EAM3EO,GAAyBf,EAAAA,EAAAA,IAAO,0B,OACtCgB,EAAAA,EAAAA,IAAMnB,GAAaoB,IAEfP,QAAQC,IAAI,kBAAkBV,GAC9BS,QAAQC,IAAI,oBAAoBT,GAEhCK,EAAaJ,EAAgBM,OAC7BM,EAAuBN,MAAQQ,CAAK,I,4yCC3FxC,MAAMC,EAAc,EAEpB,O", "sources": ["webpack://wccsfront/./src/components/FunctionBarFold.vue", "webpack://wccsfront/./src/components/FunctionBarFold.vue?e9bd"], "sourcesContent": ["<template>\n    <el-menu default-active=\"2\" class=\"el-menu-vertical-demo\" :collapse=\"isCollapse\" @select=\"handleSelect\">\n        <el-sub-menu index=\"1\" class=\"functionList1\">\n            <template #title>\n                <el-icon>\n                    <img src=\"../assets/评论.png\" alt=\"logo\" style=\"width: 22px; height: 22px; \">\n                </el-icon>\n                <span style=\"font-size: 15px;\">会话存档</span>\n            </template>\n            <div>\n                <el-menu-item index=\"1-1\">员工会话</el-menu-item>\n                <el-menu-item index=\"1-2\">客户会话</el-menu-item>\n                <el-menu-item index=\"1-3\">群聊会话</el-menu-item>\n            </div>\n\n        </el-sub-menu>\n\n\n        <el-menu-item index=\"toggle-collapse\" class=\"collapse\" @click=\"isCollapse = !isCollapse\">\n            <el-icon>\n                <ArrowLeft :style=\"{ display: isCollapse ? 'inline-block' : 'none' }\" />\n                <ArrowRight :style=\"{ display: isCollapse ? 'none' : 'inline-block' }\" />\n            </el-icon>\n            <span v-if=\"!isCollapse\" style=\"font-size: 14px;\">收起导航</span>\n        </el-menu-item>\n\n    </el-menu>\n</template>\n\n<script setup>\nimport { ArrowLeft, ArrowRight } from '@element-plus/icons-vue'\n// import { el } from 'element-plus/es/locale'\nimport { inject, ref, watch } from 'vue'\n\n\n//面板折叠/展开状态：true or false\nconst isCollapse = ref(true)\n\n//面包屑组件\nconst breadcrumbItems = inject('breadcrumbItems')\n\nconst currentComponent_List = inject('currentComponent_List')\nconst currentComponent_ChatList = inject('currentComponent_ChatList')\n\nconst currentFunction = inject('currentFunction','1-1')\n\n//inject 当前选中的员工信息、客户信息、会话信息，在切换功能时，需要重置这些信息\nconst selectedEmployee = inject('selectedEmployee')\nconst selectedCustomer = inject('selectedCustomer')\nconst selectedChat = inject('selectedChat')\n\nconst handleSelect = (index) => {\n\n    //重置当前选中的员工信息、客户信息、会话信息\n    selectedEmployee.value = null\n    selectedCustomer.value = null\n    selectedChat.value = null\n    \n    if(index == 'toggle-collapse'){\n        return;\n    }\n    console.log('test15,当前选中功能：',index)\n    currentFunction.value = index\n\n    const items = {\n        '1-1': ['会话存档', '员工会话'],\n        '1-2': ['会话存档', '客户会话'],\n        '1-3': ['会话存档', '群聊会话'],\n    }\n    breadcrumbItems.value = items[index] || ['会话存档', '员工会话']\n\n    const panels_List = {\n        '1-1': 'EmployeeList',\n        '1-2': 'CustomerList',\n        '1-3': 'GroupList',\n    }\n    currentComponent_List.value = panels_List[index] || 'EmployeeList'\n\n    const panels_ChatList = {\n        '1-1': 'EmployeeChatList',\n        '1-2': 'CustomerChatList',\n        '1-3': 'GroupChatList',\n    }\n    currentComponent_ChatList.value = panels_ChatList[index] || 'EmployeeChatList'\n}\n\n\n\n//记录面板折叠/展开状态\nconst FunctionBar_isCollapse = inject('FunctionBar_isCollapse')\nwatch(isCollapse, (newVal) => {\n    \n    console.log('test13:当前选中的面板是',currentComponent_List)\n    console.log('test13:当前选中的会话面板是',currentComponent_ChatList)\n\n    handleSelect(currentFunction.value)\n    FunctionBar_isCollapse.value = newVal\n})\n\n\n</script>\n\n<style>\n.el-menu-vertical-demo {\n    height: calc(100vh - 50px);\n    width: 50px;\n\n    display: flex;\n    flex-direction: column;\n\n    margin-left: 4px;\n\n    background-color: rgb(247, 247, 247);\n\n}\n\n.el-menu-vertical-demo:not(.el-menu--collapse) {\n    width: 140px;\n}\n\n.collapse {\n    display: flex;\n    flex-direction: row;\n    margin-top: auto;\n\n}\n\n.el-sub-menu__title {\n    padding: 0px !important;\n}\n\n.el-sub-menu .el-menu-item {\n    font-size: 14px;\n}\n\n.el-menu-item.collapse {\n    padding: 0 10px !important;\n}\n\n\n.functionList1 .el-sub-menu__title {\n    padding: 0 11px !important;\n}\n\n.el-menu--collapse .el-menu .el-submenu,\n.el-menu--popup {\n    min-width: 120px !important;\n    padding: 0px !important;\n}\n</style>", "import script from \"./FunctionBarFold.vue?vue&type=script&setup=true&lang=js\"\nexport * from \"./FunctionBarFold.vue?vue&type=script&setup=true&lang=js\"\n\nimport \"./FunctionBarFold.vue?vue&type=style&index=0&id=79105302&lang=css\"\n\nconst __exports__ = script;\n\nexport default __exports__"], "names": ["isCollapse", "ref", "breadcrumbItems", "inject", "currentComponent_List", "currentComponent_ChatList", "currentFunction", "selectedEmployee", "selectedCustomer", "selectedC<PERSON>", "handleSelect", "index", "value", "console", "log", "items", "panels_List", "panels_ChatList", "FunctionBar_isCollapse", "watch", "newVal", "__exports__"], "sourceRoot": ""}