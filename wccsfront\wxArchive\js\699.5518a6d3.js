"use strict";(self["webpackChunkwccsfront"]=self["webpackChunkwccsfront"]||[]).push([[699],{7699:function(e,o,a){a.r(o),a.d(o,{default:function(){return g}});var l=a(6768),s=a(5130);const r={class:"login-container"},t={class:"login-box"};function n(e,o,a,n,c,m){const u=(0,l.g2)("el-input"),d=(0,l.g2)("el-form-item"),i=(0,l.g2)("el-button"),g=(0,l.g2)("el-form");return(0,l.uX)(),(0,l.CE)("body",r,[o[4]||(o[4]=(0,l.Lk)("div",{class:"background"},[(0,l.Lk)("div",{class:"shape"}),(0,l.Lk)("div",{class:"shape"})],-1)),(0,l.Lk)("div",t,[o[3]||(o[3]=(0,l.Lk)("h2",null,"用户登录",-1)),(0,l.bF)(g,{class:"form",model:c.form,onSubmit:(0,s.D$)(m.handleLogin,["prevent"])},{default:(0,l.k6)((()=>[(0,l.bF)(d,{class:"form-item",label:"用户名"},{default:(0,l.k6)((()=>[(0,l.bF)(u,{class:"input1",modelValue:c.form.username,"onUpdate:modelValue":o[0]||(o[0]=e=>c.form.username=e),placeholder:"请输入用户名"},null,8,["modelValue"])])),_:1}),(0,l.bF)(d,{class:"form-item",label:"密码"},{default:(0,l.k6)((()=>[(0,l.bF)(u,{class:"input2",modelValue:c.form.password,"onUpdate:modelValue":o[1]||(o[1]=e=>c.form.password=e),type:"password",placeholder:"请输入密码","show-password":""},null,8,["modelValue"])])),_:1}),(0,l.bF)(i,{class:"login-btn",type:"primary","native-type":"submit"},{default:(0,l.k6)((()=>o[2]||(o[2]=[(0,l.eW)("登录")]))),_:1})])),_:1},8,["model","onSubmit"])])])}a(4114);var c=a(1219),m=a(1314),u={name:"UserLogin",data(){return{form:{username:"",password:""}}},methods:{handleLogin(){this.form.username?this.form.password?m.A.post("/api/user/login",this.form).then((e=>{if(console.log(e),0===e.data.code){localStorage.setItem("user",this.form.username),localStorage.setItem("access_token",e.data.data.access_token);const o=Date.now()+1e3*e.data.data.expire;localStorage.setItem("expireTime",o),console.log("log2——登录测试，localStroage中存储的用户名：",localStorage.getItem("user")),console.log("log4——登录测试，localStroage中存储的下次失效时间点：",localStorage.getItem("expireTime")),console.log("log6——登录测试，当前时间点：",Date.now()),this.$router.push("/wxArchive")}else console.log("log5——登录测试，请求成功但登录失败（用户密码错误）：",e.data),c.nk.error("登录失败，请检查用户名和密码")})).catch((e=>{console.log(e),c.nk.error("登录请求出错，请稍后重试")})):c.nk.error("请输入密码"):c.nk.error("请输入用户名")}}},d=a(1241);const i=(0,d.A)(u,[["render",n]]);var g=i}}]);
//# sourceMappingURL=699.5518a6d3.js.map