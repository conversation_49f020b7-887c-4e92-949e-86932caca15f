<!-- src/views/Login.vue -->
<template>

    <body class="login-container">
        <div class="background">
            <div class="shape"></div>
            <div class="shape"></div>
        </div>
        <div class="login-box">
            <h2>用户登录</h2>
            <el-form class="form" :model="form" @submit.prevent="handleLogin">
                <el-form-item class="form-item" label="用户名" >
                    <el-input class=" input1" v-model="form.username" placeholder="请输入用户名" />
                </el-form-item>
                <el-form-item class="form-item" label="密码">
                    <el-input class="input2" v-model="form.password" type="password" placeholder="请输入密码"
                        show-password />
                </el-form-item>
                <el-button class="login-btn" type="primary" native-type="submit">登录</el-button>
            </el-form>
        </div>

    </body>


</template>

<script>
import { ElMessage } from 'element-plus'
import axiosInstance from '../axiosConfig.js'; // 引入 axios 实例

export default {
    name: 'UserLogin',

    data() {
        return {
            form: {
                username: '',
                password: ''
            }
        };
    },

    methods: {
        handleLogin() {
            if (!this.form.username) {
                ElMessage.error('请输入用户名');
                return;
            }
            if (!this.form.password) {
                ElMessage.error('请输入密码');
                return;
            }
            axiosInstance.post('/api/user/login', this.form).then(res => {
                console.log(res);
                if (res.data.code === 0) {
                    localStorage.setItem('user', this.form.username);
                    localStorage.setItem('access_token', res.data.data.access_token);
                    const expirationTime = Date.now() + res.data.data.expire * 1000; // 计算失效时间点（毫秒）
                    localStorage.setItem('expireTime', expirationTime);
                    console.log("log2——登录测试，localStroage中存储的用户名：", localStorage.getItem('user'));
                    console.log("log4——登录测试，localStroage中存储的下次失效时间点：", localStorage.getItem('expireTime'));
                    console.log("log6——登录测试，当前时间点：", Date.now());
                    this.$router.push('/wxArchive');
                } else {
                    console.log("log5——登录测试，请求成功但登录失败（用户密码错误）：", res.data);
                    ElMessage.error('登录失败，请检查用户名和密码');
                }
            }).catch(error => {
                console.log(error);
                ElMessage.error('登录请求出错，请稍后重试');//可能的情况：1. 网络问题；2. 后端服务器未启动
            });
        }
    }
};
</script>


<style>
*， *::before,
*::after {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
}

body {
    background-color: #569ff7;
}

.background {

    width: 300px;
    height: 200px;
    /* background-color: aliceblue; */

    /* 实现绝对居中的方法： */
    position: absolute;
    transform: translate(-50%, -50%);
    left: 50%;
    top: 50%;
}

.background .shape {
    position: absolute;
    height: 150px;
    width: 150px;
    border-radius: 50%;
}

.background .shape:nth-child(1) {
    background: linear-gradient(#0F71E6, #23a2f6);
    left: -80px;
    top: -110px;
}

.background .shape:nth-child(2) {
    right: -80px;
    bottom: -80px;
    background: linear-gradient(to right, #f6d365, #fda085);
}

.login-box * {
    font-family: 'Microsoft YaHei', sans-serif;
    /* color: #ffff; */
    letter-spacing: 0.5px;
    outline: none;
    border: none;

}

.login-box {
    height: 200px;
    width: 280px;
    background-color: rgba(255, 255, 255, 0.13);
    position: absolute;
    transform: translate(-50%, -50%);
    left: 50%;
    top: 50%;

    border-radius: 10px;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 0 40px rgba(8, 7, 16, 0.6);
    padding: 50px 35px;
}

.login-box h2 {
    font-size: 20px;
    text-align: center;
    font-weight: 500;
    line-height: 42px;
    color: white;
}

.login-box .form {
    margin-top: 30px;
}

.el-form-item__label {
    color: white;
}





.login-box .login-btn {
    position: absolute;
    transform: translate(-50%, -50%);
    left: 50%;
    top: 80%;
    margin-top: 10px;
    width: 100px;
}

.login-box .input2 {
    margin-left: 13px;
    color: black;
}



.el-input {
    /*获取焦点后的边框颜色*/
    --el-input-focus-border-color: rgb(0, 140, 255);
    /*鼠标悬停边框颜色*/
    --el-input-hover-border-color: #00c3ff;
    --el-input-text-color: black;
    transition: box-shadow 0.3s ease;
}

.el-input:hover {
    box-shadow: 0 0 10px rgba(188, 239, 255, 0.959);
    /* 调整阴影参数，比如模糊、颜色透明度等 */
}
</style>
