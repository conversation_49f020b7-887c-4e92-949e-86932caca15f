{"ast": null, "code": "'use strict';\n\nimport \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport toFormData from './toFormData.js';\n\n/**\n * It encodes a string by replacing all characters that are not in the unreserved set with\n * their percent-encoded equivalents\n *\n * @param {string} str - The string to encode.\n *\n * @returns {string} The encoded string.\n */\nfunction encode(str) {\n  const charMap = {\n    '!': '%21',\n    \"'\": '%27',\n    '(': '%28',\n    ')': '%29',\n    '~': '%7E',\n    '%20': '+',\n    '%00': '\\x00'\n  };\n  return encodeURIComponent(str).replace(/[!'()~]|%20|%00/g, function replacer(match) {\n    return charMap[match];\n  });\n}\n\n/**\n * It takes a params object and converts it to a FormData object\n *\n * @param {Object<string, any>} params - The parameters to be converted to a FormData object.\n * @param {Object<string, any>} options - The options object passed to the Axios constructor.\n *\n * @returns {void}\n */\nfunction AxiosURLSearchParams(params, options) {\n  this._pairs = [];\n  params && toFormData(params, this, options);\n}\nconst prototype = AxiosURLSearchParams.prototype;\nprototype.append = function append(name, value) {\n  this._pairs.push([name, value]);\n};\nprototype.toString = function toString(encoder) {\n  const _encode = encoder ? function (value) {\n    return encoder.call(this, value, encode);\n  } : encode;\n  return this._pairs.map(function each(pair) {\n    return _encode(pair[0]) + '=' + _encode(pair[1]);\n  }, '').join('&');\n};\nexport default AxiosURLSearchParams;", "map": {"version": 3, "names": ["toFormData", "encode", "str", "charMap", "encodeURIComponent", "replace", "replacer", "match", "AxiosURLSearchParams", "params", "options", "_pairs", "prototype", "append", "name", "value", "push", "toString", "encoder", "_encode", "call", "map", "each", "pair", "join"], "sources": ["D:/Code/VUE/WCCSFront/wccsfront/node_modules/axios/lib/helpers/AxiosURLSearchParams.js"], "sourcesContent": ["'use strict';\n\nimport toFormData from './toFormData.js';\n\n/**\n * It encodes a string by replacing all characters that are not in the unreserved set with\n * their percent-encoded equivalents\n *\n * @param {string} str - The string to encode.\n *\n * @returns {string} The encoded string.\n */\nfunction encode(str) {\n  const charMap = {\n    '!': '%21',\n    \"'\": '%27',\n    '(': '%28',\n    ')': '%29',\n    '~': '%7E',\n    '%20': '+',\n    '%00': '\\x00'\n  };\n  return encodeURIComponent(str).replace(/[!'()~]|%20|%00/g, function replacer(match) {\n    return charMap[match];\n  });\n}\n\n/**\n * It takes a params object and converts it to a FormData object\n *\n * @param {Object<string, any>} params - The parameters to be converted to a FormData object.\n * @param {Object<string, any>} options - The options object passed to the Axios constructor.\n *\n * @returns {void}\n */\nfunction AxiosURLSearchParams(params, options) {\n  this._pairs = [];\n\n  params && toFormData(params, this, options);\n}\n\nconst prototype = AxiosURLSearchParams.prototype;\n\nprototype.append = function append(name, value) {\n  this._pairs.push([name, value]);\n};\n\nprototype.toString = function toString(encoder) {\n  const _encode = encoder ? function(value) {\n    return encoder.call(this, value, encode);\n  } : encode;\n\n  return this._pairs.map(function each(pair) {\n    return _encode(pair[0]) + '=' + _encode(pair[1]);\n  }, '').join('&');\n};\n\nexport default AxiosURLSearchParams;\n"], "mappings": "AAAA,YAAY;;AAAC;AAAA;AAAA;AAEb,OAAOA,UAAU,MAAM,iBAAiB;;AAExC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,MAAMA,CAACC,GAAG,EAAE;EACnB,MAAMC,OAAO,GAAG;IACd,GAAG,EAAE,KAAK;IACV,GAAG,EAAE,KAAK;IACV,GAAG,EAAE,KAAK;IACV,GAAG,EAAE,KAAK;IACV,GAAG,EAAE,KAAK;IACV,KAAK,EAAE,GAAG;IACV,KAAK,EAAE;EACT,CAAC;EACD,OAAOC,kBAAkB,CAACF,GAAG,CAAC,CAACG,OAAO,CAAC,kBAAkB,EAAE,SAASC,QAAQA,CAACC,KAAK,EAAE;IAClF,OAAOJ,OAAO,CAACI,KAAK,CAAC;EACvB,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,oBAAoBA,CAACC,MAAM,EAAEC,OAAO,EAAE;EAC7C,IAAI,CAACC,MAAM,GAAG,EAAE;EAEhBF,MAAM,IAAIT,UAAU,CAACS,MAAM,EAAE,IAAI,EAAEC,OAAO,CAAC;AAC7C;AAEA,MAAME,SAAS,GAAGJ,oBAAoB,CAACI,SAAS;AAEhDA,SAAS,CAACC,MAAM,GAAG,SAASA,MAAMA,CAACC,IAAI,EAAEC,KAAK,EAAE;EAC9C,IAAI,CAACJ,MAAM,CAACK,IAAI,CAAC,CAACF,IAAI,EAAEC,KAAK,CAAC,CAAC;AACjC,CAAC;AAEDH,SAAS,CAACK,QAAQ,GAAG,SAASA,QAAQA,CAACC,OAAO,EAAE;EAC9C,MAAMC,OAAO,GAAGD,OAAO,GAAG,UAASH,KAAK,EAAE;IACxC,OAAOG,OAAO,CAACE,IAAI,CAAC,IAAI,EAAEL,KAAK,EAAEd,MAAM,CAAC;EAC1C,CAAC,GAAGA,MAAM;EAEV,OAAO,IAAI,CAACU,MAAM,CAACU,GAAG,CAAC,SAASC,IAAIA,CAACC,IAAI,EAAE;IACzC,OAAOJ,OAAO,CAACI,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGJ,OAAO,CAACI,IAAI,CAAC,CAAC,CAAC,CAAC;EAClD,CAAC,EAAE,EAAE,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;AAClB,CAAC;AAED,eAAehB,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}