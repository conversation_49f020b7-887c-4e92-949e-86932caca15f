<template>
    <el-menu default-active="2" class="el-menu-vertical-demo" :collapse="isCollapse" @select="handleSelect">
        <el-sub-menu index="1" class="functionList1">
            <template #title>
                <el-icon>
                    <img src="../assets/评论.png" alt="logo" style="width: 22px; height: 22px; ">
                </el-icon>
                <span style="font-size: 15px;">会话存档</span>
            </template>
            <div>
                <el-menu-item index="1-1">员工会话</el-menu-item>
                <el-menu-item index="1-2">客户会话</el-menu-item>
                <el-menu-item index="1-3">群聊会话</el-menu-item>
            </div>

        </el-sub-menu>


        <el-menu-item index="toggle-collapse" class="collapse" @click="isCollapse = !isCollapse">
            <el-icon>
                <ArrowRight :style="{ display: isCollapse ? 'inline-block' : 'none' }" />
                <ArrowLeft :style="{ display: isCollapse ? 'none' : 'inline-block' }" />
            </el-icon>
            <span v-if="!isCollapse" style="font-size: 14px;">收起导航</span>
        </el-menu-item>

    </el-menu>
</template>

<script setup>
import { ArrowLeft, ArrowRight } from '@element-plus/icons-vue'
// import { el } from 'element-plus/es/locale'
import { inject, ref, watch } from 'vue'


//面板折叠/展开状态：true or false
const isCollapse = ref(true)

//面包屑组件
const breadcrumbItems = inject('breadcrumbItems')

const currentComponent_List = inject('currentComponent_List')
const currentComponent_ChatList = inject('currentComponent_ChatList')

const currentFunction = inject('currentFunction','1-1')

//inject 当前选中的员工信息、客户信息、会话信息，在切换功能时，需要重置这些信息
const selectedEmployee = inject('selectedEmployee')
const selectedCustomer = inject('selectedCustomer')
const selectedChat = inject('selectedChat')

const handleSelect = (index) => {

    //重置当前选中的员工信息、客户信息、会话信息
    selectedEmployee.value = null
    selectedCustomer.value = null
    selectedChat.value = null
    
    if(index == 'toggle-collapse'){
        return;
    }
    console.log('test15,当前选中功能：',index)
    currentFunction.value = index

    const items = {
        '1-1': ['会话存档', '员工会话'],
        '1-2': ['会话存档', '客户会话'],
        '1-3': ['会话存档', '群聊会话'],
    }
    breadcrumbItems.value = items[index] || ['会话存档', '员工会话']

    const panels_List = {
        '1-1': 'EmployeeList',
        '1-2': 'CustomerList',
        '1-3': 'GroupList',
    }
    currentComponent_List.value = panels_List[index] || 'EmployeeList'

    const panels_ChatList = {
        '1-1': 'EmployeeChatList',
        '1-2': 'CustomerChatList',
        '1-3': 'GroupChatList',
    }
    currentComponent_ChatList.value = panels_ChatList[index] || 'EmployeeChatList'
}



//记录面板折叠/展开状态
const FunctionBar_isCollapse = inject('FunctionBar_isCollapse')
watch(isCollapse, (newVal) => {
    
    console.log('test13:当前选中的面板是',currentComponent_List)
    console.log('test13:当前选中的会话面板是',currentComponent_ChatList)

    handleSelect(currentFunction.value)
    FunctionBar_isCollapse.value = newVal
})


</script>

<style>
.el-menu-vertical-demo {
    height: calc(100vh - 50px);
    width: 50px;

    display: flex;
    flex-direction: column;

    margin-left: 4px;

    background-color: rgb(247, 247, 247);

}

.el-menu-vertical-demo:not(.el-menu--collapse) {
    width: 140px;
}

.collapse {
    display: flex;
    flex-direction: row;
    margin-top: auto;

}

.el-sub-menu__title {
    padding: 0px !important;
}

.el-sub-menu .el-menu-item {
    font-size: 14px;
}

.el-menu-item.collapse {
    padding: 0 10px !important;
}


.functionList1 .el-sub-menu__title {
    padding: 0 11px !important;
}

.el-menu--collapse .el-menu .el-submenu,
.el-menu--popup {
    min-width: 120px !important;
    padding: 0px !important;
}
</style>