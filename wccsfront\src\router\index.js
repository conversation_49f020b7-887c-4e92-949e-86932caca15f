// import full from 'core-js/full';
import { createRouter, createWebHistory } from 'vue-router';

const routes = [
    {
        path: '/wxArchive',
        name: 'Home',
        component: () => import('@/homePage/HomePage.vue'),
        meta: { requiresAuth: true }, // 该路由需要身份验证
    },
    {
        path: '/login',
        name: 'Login',
        component: () => import('@/loginPage/login.vue'),
    },
    {
        path: '/functionbarfold',
        name: 'FunctionBarFold',
        component: () => import('@/components/FunctionBarFold.vue'),
        meta: { requiresAuth: true }, // 该路由需要身份验证
    },
    {
        path: '/breadcrumb',
        name: 'Breadcrumb',
        component: () => import('@/components/BreadCrumb.vue'),
        meta: { requiresAuth: true }, // 该路由需要身份验证
    },

    {
        path: '/employeelist',
        name: 'EmployeeList',
        component: () => import('@/components/ChatArchive/EmployeeChat/EmployeeList.vue'),
        meta: { requiresAuth: true }, // 该路由需要身份验证
    },
    {
        path: '/customerlist',
        name: 'CustomerList',
        component: () => import('@/components/ChatArchive/CustomerChat/CustomerList.vue'),
        meta: { requiresAuth: true }, // 该路由需要身份验证
    },
    {
        path: '/grouplist',
        name: 'GroupList',
        component: () => import('@/components/ChatArchive/GroupChat/GroupList.vue'),
        meta: { requiresAuth: true }, // 该路由需要身份验证
    },
    {
        path: '/employeechatlist',
        name: 'EmployeeChatList',
        component: () => import('@/components/ChatArchive/EmployeeChat/EmployeeChatList.vue'),
        meta: { requiresAuth: true }, // 该路由需要身份验证
    },
    {
        path: '/customerchatlist',
        name: 'CustomerChatList',
        component: () => import('@/components/ChatArchive/CustomerChat/CustomerChatList.vue'),
        meta: { requiresAuth: true }, // 该路由需要身份验证
    },

];


const router = createRouter({
    // history: createWebHashHistory(),
    history: createWebHistory(),
    routes
});

// 全局路由守卫
router.beforeEach((to, from, next) => {

    const user = localStorage.getItem('user');
    const expireTime = localStorage.getItem('expireTime');

    console.log('全局路由守卫--获取localStorage的user', user)
    console.log('全局路由守卫--获取localStorage的expireTime', expireTime)
    console.log('全局路由守卫--获取当前时间', Date.now())

    if (to.meta.requiresAuth) {
        if (!user) {
            console.log('全局路由守卫--未登录', user);
            next('/login');
        } else if (expireTime < Date.now()) {
            console.log('全局路由守卫--已登录，已过期', user);
            localStorage.removeItem('user');
            localStorage.removeItem('expireTime');
            next('/login');
        } else {
            console.log('全局路由守卫--已登录，未过期', user);
            next();
        }
    } else if (to.path === '/login' && user && expireTime > Date.now()) {
        console.log('全局路由守卫--已登录，未过期，访问登录页，跳转到首页', user);
        next('/');
    } else {
        console.log('全局路由守卫--无需登录或正常访问', user);
        next();
    }
});

export default router;

