"use strict";(self["webpackChunkwccsfront"]=self["webpackChunkwccsfront"]||[]).push([[623],{7623:function(r,e,t){t.r(e),t.d(e,{default:function(){return d}});var u=t(6768),c=t(144),n=t(4232),a=t(7477);const s={class:"breadcrumb-container"};var o={__name:"BreadCrumb",setup(r){const e=(0,u.WQ)("breadcrumbItems");return(r,t)=>{const o=(0,u.g2)("el-breadcrumb-item"),b=(0,u.g2)("el-breadcrumb");return(0,u.uX)(),(0,u.CE)("div",s,[(0,u.bF)(b,{"separator-icon":(0,c.R1)(a<PERSON>)},{default:(0,u.k6)((()=>[((0,u.uX)(!0),(0,u.CE)(u.FK,null,(0,u.pI)((0,c.R1)(e),((r,e)=>((0,u.uX)(),(0,u.Wv)(o,{key:e},{default:(0,u.k6)((()=>[(0,u.eW)((0,n.v_)(r),1)])),_:2},1024)))),128))])),_:1},8,["separator-icon"])])}}};const b=o;var d=b}}]);
//# sourceMappingURL=623.0168f273.js.map