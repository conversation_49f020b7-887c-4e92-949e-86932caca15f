"use strict";(self["webpackChunkwccsfront"]=self["webpackChunkwccsfront"]||[]).push([[189],{1565:function(e,a,l){e.exports=l.p+"img/群聊头像.b7b14578.png"},5189:function(e,a,l){l.r(a),l.d(a,{default:function(){return X}});l(8111),l(2489),l(7588),l(7642),l(8004),l(3853),l(5876),l(2475),l(5024),l(1698);var t=l(6768),s=l(144),r=l(4232),u=l(1565),o=l(7477),n=l(1314),c=l(1219);const v={class:"mainContainer-grouplist"},i={class:"search",style:{width:"800px"}},d={class:"lable-navigator"},p=["onClick"],m={class:"info"},g={class:"name"},h={class:"info-detail"},f={class:"totalNum"},k={class:"owner"},b=["onClick"],L={class:"info"},C={class:"name"},_={class:"info-detail"},w={class:"totalNum"},K={class:"owner"},y=["onClick"],R={class:"info"},I={class:"name"},F={class:"info-detail"},A={class:"totalNum"},z={class:"owner"},E={class:"pageContainer"};var N={__name:"GroupList",setup(e){const a=(0,s.KR)(""),l=(0,s.KR)("first"),N=(0,s.KR)([]),V=(0,s.KR)([]),X=(0,s.KR)([]),S=(0,s.KR)(""),T=(0,s.KR)(1),x=(0,s.KR)(100),B=(0,t.WQ)("currentComponent_List"),G=(0,t.WQ)("currentComponent_ChatList"),Q=(e,a,l)=>{B.value="GroupList",G.value="";const t=localStorage.getItem("access_token");n.A.post("/api/chatmessage/chatter",{type:"groupchat",page:e,limit:a,name:l},{headers:{Authorization:"Bearer "+t}}).then((e=>{0===e.data.code&&(console.log("获取群聊列表成功",e.data.data.result),N.value=e.data.data.result,S.value=e.data.data.total,V.value=N.value.filter((e=>!e.chatType)),X.value=N.value.filter((e=>"customer"===e.chatType)),N.value.forEach((e=>{e.owner&&Y(e.owner),e.chatId&&ae(e.chatId)})))})).catch((e=>{console.log(e),c.nk.error("获取群聊列表失败，请检查网络或联系管理员")}))},U=()=>{console.log("")},W=()=>{a.value&&(T.value=1);const e=[...V.value,...X.value],t=e.filter((e=>e.name.toLowerCase().includes(a.value.toLowerCase())));switch(l.value){case"first":N.value=t,S.value=N.value.length;break;case"second":N.value=t.filter((e=>!e.chatType)),S.value=N.value.length;break;case"third":N.value=t.filter((e=>"customer"===e.chatType)),S.value=N.value.length;break}};(0,t.sV)((()=>Q(1,100,""))),(0,t.wB)(a,W);const M=e=>{switch(console.log("检测到群聊标签被点击",e.props.name),e.props.name){case"first":N.value=[...V.value,...X.value],S.value=N.value.length;break;case"second":N.value=V.value,S.value=N.value.length;break;case"third":N.value=X.value,S.value=N.value.length;break}},O=(0,t.WQ)("selectedChat",null),P=e=>{let a=e.chatId,l="",t=j(e),s="",r="",u="",o="",n="",c="groupchat",v=1,i=100;const d={from:a,to:l,fromName:t,fromLable:s,fromAvatar:r,toName:u,toAvatar:o,toLable:n,type:c,page:v,limit:i};O.value=d,console.log("到某一群聊会话被点击",O.value)},j=e=>{if(e.name)return e.name;const a=e.chatId;return a&&a.length>6?a.slice(0,11)+"...":""},q=(0,s.KR)({}),D=(0,s.KR)({}),H=(0,s.KR)(new Set),J=(0,s.KR)({}),Y=async e=>{if(J.value[e])return J.value[e];if(H.value.has(e)||D.value[e]){const a=D.value[e];return J.value[e]=a?.from_user.name||"未知用户",J.value[e]}try{H.value.add(e),console.log("调用getOwnerName方法");const a=localStorage.getItem("access_token"),l=await n.A.get("/api/chatmessage/detail/getNameAndLable",{params:{from_userId:e},headers:{Authorization:"Bearer "+a}}),t=l.data.data;return q.value[e]=t,D.value[e]=t,J.value[e]=t.from_user.name,J.value[e]}catch(a){return console.error("获取群聊owner信息失败:",a),q.value[e]={from_user:{name:"未知用户"}},D.value[e]={from_user:{name:"未知用户"}},J.value[e]="未知用户","未知用户"}finally{H.value.delete(e)}},Z=(0,s.KR)({}),$=(0,s.KR)({}),ee=(0,s.KR)(new Set),ae=async e=>{if(!ee.value.has(e)){if($.value[e]){const a=$.value[e];return Z.value[e]=a||0,a}try{H.value.add(e),console.log("调用getGroupMenberNumber方法");const a=localStorage.getItem("access_token"),l=await n.A.get("/api/chatmessage/chatter/getgroupMenberCount",{params:{chatId:e},headers:{Authorization:"Bearer "+a}}),t=l.data.data;return Z.value[e]=t,$.value[e]=t,Z.value[e]=t,Z.value[e]}catch(a){return console.error("获取群聊人数失败:",a),Z.value[e]=0,$.value[e]=0,0}finally{ee.value.delete(e)}}};return(e,n)=>{const c=(0,t.g2)("el-icon"),V=(0,t.g2)("el-input"),X=(0,t.g2)("el-tab-pane"),B=(0,t.g2)("el-tabs"),G=(0,t.g2)("el-pagination");return(0,t.uX)(),(0,t.CE)("div",v,[(0,t.Lk)("div",i,[(0,t.bF)(V,{placeholder:"搜索群聊",modelValue:a.value,"onUpdate:modelValue":n[0]||(n[0]=e=>a.value=e),clearable:"",class:"search-input"},{prefix:(0,t.k6)((()=>[(0,t.bF)(c,{class:"search-icon"},{default:(0,t.k6)((()=>[(0,t.bF)((0,s.R1)(o.Search))])),_:1})])),_:1},8,["modelValue"])]),(0,t.Lk)("div",d,[(0,t.bF)(B,{modelValue:l.value,"onUpdate:modelValue":n[1]||(n[1]=e=>l.value=e),class:"grouplist-tabs",onTabClick:M},{default:(0,t.k6)((()=>[(0,t.bF)(X,{label:"全部",name:"first"},{default:(0,t.k6)((()=>[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(N.value,(e=>((0,t.uX)(),(0,t.CE)("div",{class:"group-card",key:e.roomid,onClick:a=>P(e)},[n[3]||(n[3]=(0,t.Lk)("div",{class:"profile"},[(0,t.Lk)("img",{class:"staff-avatar",src:u,alt:"群聊头像"})],-1)),(0,t.Lk)("div",m,[(0,t.Lk)("p",g,(0,r.v_)(j(e)),1),(0,t.Lk)("div",h,[(0,t.Lk)("p",f,(0,r.v_)($.value[e.chatId]||"加载中...")+"人 | ",1),(0,t.Lk)("p",k,"群主："+(0,r.v_)(J.value[e.owner]||"加载中..."),1)])])],8,p)))),128))])),_:1}),(0,t.bF)(X,{label:"内部",name:"second"},{default:(0,t.k6)((()=>[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(N.value,(e=>((0,t.uX)(),(0,t.CE)("div",{class:"group-card",key:e.roomid,onClick:a=>P(e)},[n[4]||(n[4]=(0,t.Lk)("div",{class:"profile"},[(0,t.Lk)("img",{class:"staff-avatar",src:u,alt:"群聊头像"})],-1)),(0,t.Lk)("div",L,[(0,t.Lk)("p",C,(0,r.v_)(j(e)),1),(0,t.Lk)("div",_,[(0,t.Lk)("p",w,(0,r.v_)($.value[e.chatId]||"加载中...")+"人 | ",1),(0,t.Lk)("p",K,"群主："+(0,r.v_)(J.value[e.owner]||"加载中..."),1)])])],8,b)))),128))])),_:1}),(0,t.bF)(X,{label:"外部",name:"third"},{default:(0,t.k6)((()=>[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(N.value,(e=>((0,t.uX)(),(0,t.CE)("div",{class:"group-card",key:e.roomid,onClick:a=>P(e)},[n[5]||(n[5]=(0,t.Lk)("div",{class:"profile"},[(0,t.Lk)("img",{class:"staff-avatar",src:u,alt:"群聊头像"})],-1)),(0,t.Lk)("div",R,[(0,t.Lk)("p",I,(0,r.v_)(j(e)),1),(0,t.Lk)("div",F,[(0,t.Lk)("p",A,(0,r.v_)($.value[e.chatId]||"加载中...")+"人 | ",1),(0,t.Lk)("p",z,"群主："+(0,r.v_)(J.value[e.owner]||"加载中..."),1)])])],8,y)))),128))])),_:1})])),_:1},8,["modelValue"])]),(0,t.Lk)("div",E,[(0,t.bF)(G,{"current-page":T.value,"onUpdate:currentPage":n[2]||(n[2]=e=>T.value=e),background:"",size:e.size,layout:"total, prev, pager, next",total:S.value,"page-size":x.value,"pager-count":3,small:"",onCurrentChange:U},null,8,["current-page","size","total","page-size"])])])}}};const V=N;var X=V}}]);
//# sourceMappingURL=189.387cc0c1.js.map